import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useLocalStorage } from '@/hooks/useLocalStorage';
import { useToast } from '@/hooks/use-toast';
import { ExpenseForm } from '@/components/ExpenseForm';
import { BalanceDisplay } from '@/components/BalanceDisplay';
import { ActivityFeed } from '@/components/ActivityFeed';
import { SettlementView } from '@/components/SettlementView';
import { House, Roommate, Expense, Balance } from '@/types';
import { calculateBalances } from '@/utils/calculations';
import { formatCurrency } from '@/utils/calculations';

const Dashboard = () => {
  const [currentRoommate] = useLocalStorage<string>('currentRoommate', '');
  const [house] = useLocalStorage<House | null>('house', null);
  const [roommates] = useLocalStorage<Roommate[]>('roommates', []);
  const [expenses] = useLocalStorage<Expense[]>('expenses', []);
  const [showExpenseForm, setShowExpenseForm] = useState(false);
  const [showSettlement, setShowSettlement] = useState(false);
  const [balances, setBalances] = useState<Balance[]>([]);
  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    if (!currentRoommate) {
      toast({
        title: "Access required",
        description: "Please select your name to continue.",
        variant: "destructive",
      });
      navigate('/roommate');
      return;
    }

    if (!house) {
      toast({
        title: "No house found",
        description: "Please set up a house first.",
        variant: "destructive",
      });
      navigate('/admin');
      return;
    }

    const newBalances = calculateBalances(expenses, roommates);
    setBalances(newBalances);
  }, [currentRoommate, house, expenses, roommates, navigate, toast]);

  const handleLogout = () => {
    localStorage.removeItem('currentRoommate');
    navigate('/roommate');
  };

  if (!house || !currentRoommate) {
    return null;
  }

  const currentBalance = balances.find(b => 
    roommates.find(r => r.name === currentRoommate)?.id === b.roommateId
  );

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-gradient-primary text-white p-4">
        <div className="max-w-4xl mx-auto flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">{house.name}</h1>
            <p className="text-white/80">Welcome back, {currentRoommate}!</p>
          </div>
          <div className="flex items-center gap-3">
            {currentBalance && (
              <Badge 
                variant={currentBalance.balance >= 0 ? "secondary" : "destructive"}
                className="text-sm px-3 py-1"
              >
                {currentBalance.balance >= 0 ? '+' : ''}{formatCurrency(currentBalance.balance, house.currency)}
              </Badge>
            )}
            <Button variant="ghost" onClick={handleLogout} className="text-white hover:bg-white/20">
              Switch User
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-4 space-y-6">
        {/* Quick Actions */}
        <div className="flex flex-wrap gap-3">
          <Button 
            onClick={() => setShowExpenseForm(!showExpenseForm)}
            variant="warm"
            className="shadow-button"
          >
            {showExpenseForm ? 'Cancel' : '✨ Add Expense'}
          </Button>
          <Button 
            onClick={() => setShowSettlement(!showSettlement)}
            variant="secondary"
            className="shadow-button"
          >
            💰 Settle Up
          </Button>
          <Button 
            onClick={() => navigate('/admin')}
            variant="outline"
          >
            Admin Panel
          </Button>
        </div>

        {/* Expense Form */}
        {showExpenseForm && (
          <Card className="shadow-card">
            <CardHeader>
              <CardTitle>Add New Expense</CardTitle>
            </CardHeader>
            <CardContent>
              <ExpenseForm 
                onClose={() => setShowExpenseForm(false)}
                currentUser={currentRoommate}
              />
            </CardContent>
          </Card>
        )}

        {/* Settlement View */}
        {showSettlement && (
          <SettlementView 
            balances={balances}
            currency={house.currency}
            onClose={() => setShowSettlement(false)}
          />
        )}

        <div className="grid md:grid-cols-2 gap-6">
          {/* Balances */}
          <BalanceDisplay 
            balances={balances}
            currency={house.currency}
          />

          {/* Recent Activity */}
          <ActivityFeed 
            expenses={expenses.slice(-10)}
            roommates={roommates}
            currency={house.currency}
          />
        </div>
      </div>
    </div>
  );
};

export default Dashboard;