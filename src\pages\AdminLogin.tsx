import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { adminLogin } from '@/services/auth';

const AdminLogin = () => {
  const [houseId, setHouseId] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { setUser } = useAuth();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await adminLogin(houseId, password);

      if (response.success) {
        setUser(response.user);
        toast({
          title: "Welcome back!",
          description: `Successfully logged in to ${response.user.houseName}.`,
        });
        navigate('/admin/dashboard');
      }
    } catch (error: any) {
      toast({
        title: "Login failed",
        description: error.message || "Invalid house ID or password.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-primary flex items-center justify-center p-4">
      <Card className="w-full max-w-md shadow-card bg-card">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-primary">
            House Wallet
          </CardTitle>
          <CardDescription className="text-muted-foreground">
            Admin access to manage your house
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleLogin} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="houseId">House ID</Label>
              <Input
                id="houseId"
                type="text"
                value={houseId}
                onChange={(e) => setHouseId(e.target.value)}
                placeholder="Enter house ID"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter admin password"
                required
              />
            </div>
            <Button 
              type="submit" 
              className="w-full shadow-button" 
              disabled={loading}
            >
              {loading ? 'Signing in...' : 'Sign In'}
            </Button>
            <div className="text-center">
              <Button 
                type="button" 
                variant="ghost" 
                onClick={() => navigate('/roommate')}
                className="text-sm"
              >
                Continue as roommate instead
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminLogin;