import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useLocalStorage } from '@/hooks/useLocalStorage';
import { Roommate } from '@/types';

const RoommateLogin = () => {
  const [customName, setCustomName] = useState('');
  const [roommates] = useLocalStorage<Roommate[]>('roommates', []);
  const [, setCurrentRoommate] = useLocalStorage<string>('currentRoommate', '');
  const navigate = useNavigate();

  const handleRoommateLogin = (roommateName: string) => {
    setCurrentRoommate(roommateName);
    navigate('/dashboard');
  };

  const handleCustomLogin = (e: React.FormEvent) => {
    e.preventDefault();
    if (customName.trim()) {
      handleRoommateLogin(customName.trim());
    }
  };

  return (
    <div className="min-h-screen bg-gradient-secondary flex items-center justify-center p-4">
      <Card className="w-full max-w-md shadow-card bg-card">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-secondary">
            Welcome Home! 🏠
          </CardTitle>
          <CardDescription className="text-muted-foreground">
            Choose your name to continue
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {roommates.length > 0 && (
            <div className="space-y-3">
              <Label className="text-sm font-medium">Quick Select</Label>
              <div className="flex flex-wrap gap-2">
                {roommates.map((roommate) => (
                  <Badge
                    key={roommate.id}
                    variant="secondary"
                    className="cursor-pointer hover:bg-secondary-glow transition-colors px-3 py-2"
                    onClick={() => handleRoommateLogin(roommate.name)}
                  >
                    {roommate.name}
                  </Badge>
                ))}
              </div>
            </div>
          )}
          
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-card px-2 text-muted-foreground">
                Or enter your name
              </span>
            </div>
          </div>

          <form onSubmit={handleCustomLogin} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="customName">Your Name</Label>
              <Input
                id="customName"
                type="text"
                value={customName}
                onChange={(e) => setCustomName(e.target.value)}
                placeholder="Enter your name"
                required
              />
            </div>
            <Button type="submit" className="w-full shadow-button">
              Continue
            </Button>
          </form>

          <div className="text-center">
            <Button 
              type="button" 
              variant="ghost" 
              onClick={() => navigate('/admin')}
              className="text-sm"
            >
              Admin Login
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RoommateLogin;