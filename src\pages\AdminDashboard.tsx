import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useLocalStorage } from '@/hooks/useLocalStorage';
import { useToast } from '@/hooks/use-toast';
import { House, Roommate, Expense } from '@/types';

const AdminDashboard = () => {
  const [house, setHouse] = useLocalStorage<House | null>('house', null);
  const [roommates, setRoommates] = useLocalStorage<Roommate[]>('roommates', []);
  const [expenses, setExpenses] = useLocalStorage<Expense[]>('expenses', []);
  const [newRoommateName, setNewRoommateName] = useState('');
  const [houseName, setHouseName] = useState('');
  const [houseCurrency, setHouseCurrency] = useState('₺');
  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    const adminLoggedIn = localStorage.getItem('adminLoggedIn');
    if (!adminLoggedIn) {
      navigate('/admin');
      return;
    }
  }, [navigate]);

  const handleCreateHouse = (e: React.FormEvent) => {
    e.preventDefault();
    if (!houseName.trim()) return;

    const newHouse: House = {
      id: Date.now().toString(),
      name: houseName.trim(),
      currency: houseCurrency,
      createdAt: new Date()
    };

    setHouse(newHouse);
    toast({
      title: "🏠 House created!",
      description: `${newHouse.name} is ready for roommates.`,
    });
  };

  const handleAddRoommate = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newRoommateName.trim()) return;

    if (roommates.some(r => r.name.toLowerCase() === newRoommateName.toLowerCase())) {
      toast({
        title: "Name already exists",
        description: "A roommate with this name already exists.",
        variant: "destructive",
      });
      return;
    }

    const newRoommate: Roommate = {
      id: Date.now().toString(),
      name: newRoommateName.trim(),
      houseId: house?.id || 'default',
      createdAt: new Date()
    };

    setRoommates([...roommates, newRoommate]);
    setNewRoommateName('');
    
    toast({
      title: "✅ Roommate added!",
      description: `${newRoommate.name} can now log in and add expenses.`,
    });
  };

  const handleDeleteRoommate = (id: string) => {
    const roommate = roommates.find(r => r.id === id);
    if (!roommate) return;

    setRoommates(roommates.filter(r => r.id !== id));
    toast({
      title: "Roommate removed",
      description: `${roommate.name} has been removed from the house.`,
    });
  };

  const handleClearData = () => {
    if (confirm('Are you sure you want to clear all data? This cannot be undone.')) {
      setExpenses([]);
      toast({
        title: "Data cleared",
        description: "All expense data has been cleared.",
      });
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('adminLoggedIn');
    navigate('/admin');
  };

  const exportData = () => {
    const data = {
      house,
      roommates,
      expenses,
      exportedAt: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `house-wallet-${house?.name || 'data'}-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    toast({
      title: "Data exported",
      description: "Your house data has been downloaded.",
    });
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="bg-gradient-primary text-white p-4">
        <div className="max-w-4xl mx-auto flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">Admin Dashboard</h1>
            <p className="text-white/80">Manage your house and roommates</p>
          </div>
          <div className="flex gap-3">
            <Button variant="ghost" onClick={() => navigate('/dashboard')} className="text-white hover:bg-white/20">
              View Dashboard
            </Button>
            <Button variant="ghost" onClick={handleLogout} className="text-white hover:bg-white/20">
              Logout
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-4 space-y-6">
        {/* House Setup */}
        {!house ? (
          <Card className="shadow-card">
            <CardHeader>
              <CardTitle>🏠 Create Your House</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleCreateHouse} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="houseName">House Name</Label>
                    <Input
                      id="houseName"
                      value={houseName}
                      onChange={(e) => setHouseName(e.target.value)}
                      placeholder="e.g., The Awesome House"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="currency">Currency</Label>
                    <Select value={houseCurrency} onValueChange={setHouseCurrency}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="₺">₺ Turkish Lira</SelectItem>
                        <SelectItem value="$">$ US Dollar</SelectItem>
                        <SelectItem value="€">€ Euro</SelectItem>
                        <SelectItem value="£">£ British Pound</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <Button type="submit" variant="warm" className="w-full">
                  Create House
                </Button>
              </form>
            </CardContent>
          </Card>
        ) : (
          <>
            {/* House Info */}
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  🏠 {house.name}
                  <Badge variant="secondary">{house.currency}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex gap-3">
                  <Button onClick={exportData} variant="outline" size="sm">
                    📊 Export Data
                  </Button>
                  <Button onClick={handleClearData} variant="destructive" size="sm">
                    🗑️ Clear All Data
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Roommate Management */}
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle>👥 Roommates ({roommates.length})</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <form onSubmit={handleAddRoommate} className="flex gap-2">
                  <Input
                    value={newRoommateName}
                    onChange={(e) => setNewRoommateName(e.target.value)}
                    placeholder="Enter roommate name"
                    required
                  />
                  <Button type="submit" variant="success">
                    Add
                  </Button>
                </form>
                
                {roommates.length > 0 && (
                  <div className="space-y-2">
                    {roommates.map((roommate) => (
                      <div key={roommate.id} className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                        <span className="font-medium">{roommate.name}</span>
                        <Button 
                          onClick={() => handleDeleteRoommate(roommate.id)}
                          variant="destructive" 
                          size="sm"
                        >
                          Remove
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Stats */}
            <div className="grid md:grid-cols-3 gap-4">
              <Card className="shadow-card">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-primary">{roommates.length}</div>
                  <div className="text-sm text-muted-foreground">Roommates</div>
                </CardContent>
              </Card>
              <Card className="shadow-card">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-primary">{expenses.length}</div>
                  <div className="text-sm text-muted-foreground">Total Expenses</div>
                </CardContent>
              </Card>
              <Card className="shadow-card">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-primary">
                    {expenses.reduce((sum, exp) => sum + exp.amount, 0).toFixed(2)} {house.currency}
                  </div>
                  <div className="text-sm text-muted-foreground">Total Amount</div>
                </CardContent>
              </Card>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default AdminDashboard;