import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const Index = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-warm flex items-center justify-center p-4">
      <Card className="w-full max-w-md shadow-glow bg-card">
        <CardHeader className="text-center">
          <CardTitle className="text-3xl font-bold text-card-foreground mb-2">
            🏠 House Wallet
          </CardTitle>
          <CardDescription className="text-muted-foreground text-lg">
            Split expenses with your roommates, stress-free
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <Button 
              onClick={() => navigate('/roommate')}
              variant="secondary"
              className="w-full h-12 text-lg shadow-button"
            >
              🚀 Quick Start
            </Button>
            <Button 
              onClick={() => navigate('/admin')}
              variant="default"
              className="w-full h-12 shadow-button"
            >
              🔧 Admin Setup
            </Button>
          </div>
          
          <div className="pt-4 text-center text-muted-foreground text-sm space-y-2">
            <p>✨ No passwords for roommates</p>
            <p>💰 Smart expense splitting</p>
            <p>📊 Real-time balance tracking</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Index;
